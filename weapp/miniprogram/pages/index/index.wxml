<view class="container">
  <!-- 顶部装饰 -->
  <view class="top-decoration">
    <view class="decoration-circle circle-1"></view>
    <view class="decoration-circle circle-2"></view>
    <view class="decoration-circle circle-3"></view>
  </view>

  <!-- 标题区域 -->
  <view class="header">
    <view class="logo-container">
      <text class="logo-icon">♠️</text>
      <view class="title-group">
        <text class="title">德州扑克训练</text>
        <text class="subtitle">智能教学系统 · 数据统计分析</text>
      </view>
    </view>
  </view>

  <!-- 签到模块 -->
  <view class="checkin-section" >
    <view class="checkin-card">
      <view class="checkin-header">
        <view class="checkin-title">
          <text class="checkin-icon">📅</text>
          <text class="checkin-text">每日签到</text>
        </view>
        <view class="checkin-streak">
          <text class="streak-text">连续{{checkInStatus.consecutiveDays}}天</text>
        </view>
      </view>

      <view class="checkin-content">
        <view class="checkin-reward">
          <text class="reward-text">明日奖励：{{checkInStatus.nextReward.amount}}{{checkInStatus.nextReward.type === 'experience' ? '经验' : '金币'}}</text>
        </view>

        <view class="checkin-action">
          <button
                  class="checkin-btn {{checkInStatus.hasCheckedToday ? 'checked' : 'available'}}"
                  bindtap="onCheckIn"
                  disabled="{{checkInStatus.hasCheckedToday}}">
            {{checkInStatus.hasCheckedToday ? '✓ 已签到' : '签到'}}
          </button>
        </view>
      </view>

      <view class="checkin-progress">
        <view class="progress-dots">
          <block wx:for="{{7}}" wx:key="*this">
            <view class="progress-dot {{index < (checkInStatus.consecutiveDays % 7) ? 'active' : ''}}"></view>
          </block>
        </view>
        <text class="progress-text">本周进度 {{checkInStatus.consecutiveDays % 7}}/7</text>
      </view>
    </view>
  </view>

  <!-- 主要功能区域 - 开始游戏 -->
  <view class="main-section">
    <!-- 开始游戏按钮 -->
    <view class="primary-action-full">
      <button class="action-btn primary" bindtap="onStart">
        <view class="btn-content">
          <text class="btn-icon">🎮</text>
          <view class="btn-text-group">
            <text class="btn-text">开始游戏</text>
            <text class="btn-desc">与智能系统对战，提升技能</text>
          </view>
        </view>
        <view class="btn-arrow">→</view>
      </button>
    </view>
  </view>

  <!-- 次要功能按钮 -->
  <view class="main-actions">

    <view class="secondary-actions">
      <!-- 用户信息卡片 -->
      <view class="action-card user-info-card" wx:if="{{userProfile}}">
        <view class="card-header">
          <view class="icon-container user-info">
            <image class="user-avatar-small" src="{{userProfile.avatar}}" mode="aspectFill"></image>
          </view>
          <view class="card-badge">Lv.{{userProfile.level}}</view>
        </view>
        <view class="card-content">
          <text class="card-title">{{userProfile.nickname}}</text>
          <text class="card-desc">胜率 {{winRateDisplay}}%</text>
        </view>
      </view>

      <!-- 残局复现 -->
      <view class="action-card" bindtap="onEndgameReplay">
        <view class="card-header">
          <view class="icon-container endgame">
            <text class="btn-icon">🎯</text>
          </view>
          <view class="card-badge">练习</view>
        </view>
        <view class="card-content">
          <text class="card-title">残局复现</text>
          <text class="card-desc">重现经典残局，提升决策能力</text>
        </view>
      </view>
    </view>

    <!-- 新增功能区域 -->
    <view class="new-features-actions">
      <view class="action-card" bindtap="onBattleReports">
        <view class="card-header">
          <view class="icon-container battle-report">
            <text class="btn-icon">📋</text>
          </view>
          <view class="card-badge">分析</view>
        </view>
        <view class="card-content">
          <text class="card-title">我的战报</text>
          <text class="card-desc">智能分析每局游戏表现</text>
        </view>
      </view>

      <!-- 原模块2：智能代打 - 移到最后 -->
      <view class="action-card disabled" bindtap="goToAutoPlay">
        <view class="card-header">
          <view class="icon-container autoplay">
            <text class="btn-icon">🤖</text>
          </view>
          <view class="card-badge developing">开发中</view>
        </view>
        <view class="card-content">
          <text class="card-title">智能代打</text>
          <text class="card-desc">AI智能代打，自动游戏</text>
        </view>
        <view class="developing-overlay">
          <text class="developing-text">开发中</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 功能特色 -->
  <view class="features">
    <view class="features-title">
      <text>✨ 核心特色</text>
    </view>
    <view class="features-grid">
      <view class="feature-item">
        <view class="feature-icon-wrapper">
          <text class="feature-icon">🤖</text>
        </view>
        <text class="feature-title">智能教学系统</text>
        <text class="feature-desc">实时提示和策略分析</text>
      </view>
      <view class="feature-item">
        <view class="feature-icon-wrapper">
          <text class="feature-icon">📈</text>
        </view>
        <text class="feature-title">数据统计</text>
        <text class="feature-desc">详细的游戏数据分析</text>
      </view>
      <view class="feature-item">
        <view class="feature-icon-wrapper">
          <text class="feature-icon">🏆</text>
        </view>
        <text class="feature-title">成就系统</text>
        <text class="feature-desc">解锁成就，获得奖励</text>
      </view>
    </view>
  </view>

  <!-- 底部装饰 -->
  <view class="bottom-decoration">
    <view class="wave wave-1"></view>
    <view class="wave wave-2"></view>
  </view>


</view>