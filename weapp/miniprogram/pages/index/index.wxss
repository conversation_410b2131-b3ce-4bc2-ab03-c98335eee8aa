/* 首页样式 */
.container {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #1a5c2e 0%, #2d8f47 50%, #4caf50 100%);
  overflow-x: hidden;
  overflow-y: auto;
  padding-bottom: 40rpx;
}

/* 顶部装饰 */
.top-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 150rpx;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: -100rpx;
  right: -50rpx;
  animation: float 6s ease-in-out infinite;
}

.circle-2 {
  width: 120rpx;
  height: 120rpx;
  top: 50rpx;
  left: -30rpx;
  animation: float 4s ease-in-out infinite reverse;
}

.circle-3 {
  width: 80rpx;
  height: 80rpx;
  top: 150rpx;
  right: 100rpx;
  animation: float 5s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20rpx); }
}

/* 用户状态栏 */
.user-status {
  position: relative;
  z-index: 2;
  margin: 10rpx 30rpx 0;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 12rpx 25rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 26rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 4rpx;
}

.user-level {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.2);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  align-self: flex-start;
}

.user-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.stat-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: bold;
}

/* 主要功能区域 - 用户信息和开始游戏的左右布局 */
.main-section {
  margin: 30rpx 30rpx 20rpx 30rpx;
  display: flex;
  gap: 20rpx;
  align-items: stretch;
  animation: slideInUp 0.6s ease-out 0.6s both;
}

/* 紧凑版用户状态栏 */
.user-status-compact {
  min-height: 120rpx;
  padding: 20rpx;
  flex: 1;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 140rpx;
}

.user-status-compact .user-info {
  margin-bottom: 10rpx;
}

.user-status-compact .user-stats {
  align-items: flex-start;
}

/* 紧凑版开始游戏按钮 */
.primary-action-compact {
  flex: 2;
}

.primary-action-compact .action-btn {
  height: 100%;
  min-height: 140rpx;
  display: flex;
  align-items: center;
}

/* 标题区域 */
.header {
  padding: 10rpx 30rpx 20rpx;
  position: relative;
  z-index: 1;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
}

.logo-icon {
  font-size: 80rpx;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
}

.title-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 300;
}

/* 主要功能按钮 */
.main-actions {
  padding: 0 30rpx;
  margin-bottom: 20rpx;
}

.primary-action {
  margin-bottom: 15rpx;
}

.action-btn {
  width: 100%;
  border: none;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.action-btn:active::before {
  left: 100%;
}

.action-btn:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.action-btn.primary {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 25rpx;
  border-radius: 16rpx;
  min-height: 160rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.btn-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.btn-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.btn-text-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.btn-text {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 6rpx;
}

.btn-desc {
  font-size: 22rpx;
  opacity: 0.9;
}

.btn-arrow {
  font-size: 32rpx;
  font-weight: bold;
  opacity: 0.8;
}

/* 次要按钮 - 卡片式设计 */
.secondary-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin: 0 30rpx;
  width: calc(100% - 60rpx);
  box-sizing: border-box;
}

.secondary-actions .action-card {
  min-height: 160rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.action-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 25rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-height: 140rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.action-card:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.action-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
}

/* 卡片悬浮效果 */
.action-card {
  animation: cardFloat 6s ease-in-out infinite;
}

.action-card:nth-child(1) { animation-delay: 0s; }
.action-card:nth-child(2) { animation-delay: 2s; }
.action-card:nth-child(3) { animation-delay: 4s; }

@keyframes cardFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-2rpx); }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.icon-container {
  width: 55rpx;
  height: 55rpx;
  border-radius: 14rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.icon-container.stats {
  background: linear-gradient(135deg, #667eea, #764ba2);
  box-shadow: 0 8rpx 16rpx rgba(102, 126, 234, 0.3);
}

.icon-container.teaching {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  box-shadow: 0 8rpx 16rpx rgba(240, 147, 251, 0.3);
}



.icon-container.battle-report {
  background: linear-gradient(135deg, #667eea, #764ba2);
  box-shadow: 0 8rpx 16rpx rgba(102, 126, 234, 0.3);
}

.icon-container.endgame {
  background: linear-gradient(135deg, #ff9a9e, #fecfef);
  box-shadow: 0 8rpx 16rpx rgba(255, 154, 158, 0.3);
}

.icon-container.autoplay {
  background: linear-gradient(135deg, #a8edea, #fed6e3);
  box-shadow: 0 8rpx 16rpx rgba(168, 237, 234, 0.3);
}

/* 置灰状态下的图标容器 */
.action-card.disabled .icon-container.endgame {
  background: linear-gradient(135deg, #ccc, #ddd);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.action-card.disabled .icon-container.autoplay {
  background: linear-gradient(135deg, #ccc, #ddd);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.icon-container .btn-icon {
  font-size: 26rpx;
  color: white;
}

.card-badge {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 18rpx;
  font-weight: bold;
}

.card-content {
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 6rpx;
}

.card-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

/* 置灰状态样式 */
.action-card.disabled {
  position: relative;
  filter: grayscale(80%) brightness(0.7);
  opacity: 0.8;
}

.action-card.disabled::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(200, 200, 200, 0.4);
  border-radius: 20rpx;
  z-index: 1;
}

.action-card.disabled .card-content {
  position: relative;
  z-index: 0;
}

.action-card.disabled .card-header {
  position: relative;
  z-index: 0;
}

.developing-overlay {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  z-index: 2;
  pointer-events: none;
}

.developing-text {
  font-size: 18rpx;
  color: #666;
  background: rgba(255, 255, 255, 0.95);
  padding: 6rpx 10rpx;
  border-radius: 12rpx;
  font-weight: bold;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.card-badge.developing {
  background: rgba(255, 152, 0, 0.2);
  color: #e65100;
  font-weight: bold;
}

/* 新功能区域 */
.new-features-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  padding: 0 30rpx;
  margin: 20rpx 0;
}

.new-features-actions .action-card {
  min-height: 160rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}



/* 功能特色 */
.features {
  padding: 0 30rpx 20rpx;
  margin-top: 20rpx;
  position: relative;
  z-index: 1;
}

.features-title {
  text-align: center;
  margin-bottom: 15rpx;
}

.features-title text {
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  opacity: 0.9;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  align-items: stretch;
}

.feature-item {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  border-radius: 12rpx;
  padding: 20rpx 15rpx;
  text-align: center;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  min-height: 120rpx;
}

.feature-item:active {
  transform: translateY(-4rpx);
  background: rgba(255, 255, 255, 0.15);
}

.feature-icon-wrapper {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12rpx;
}

.feature-icon {
  font-size: 28rpx;
}

.feature-title {
  font-size: 22rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 6rpx;
  display: block;
}

.feature-desc {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.3;
}

/* 底部装饰 */
.bottom-decoration {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  pointer-events: none;
  overflow: hidden;
}

.wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 200%;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: wave 8s ease-in-out infinite;
}

.wave-1 {
  animation-delay: 0s;
  opacity: 0.3;
}

.wave-2 {
  animation-delay: -2s;
  opacity: 0.2;
  height: 80rpx;
}

@keyframes wave {
  0%, 100% {
    transform: translateX(-50%) translateY(0px);
  }
  50% {
    transform: translateX(-50%) translateY(-20rpx);
  }
}

/* 响应式设计 */
@media (min-width: 750rpx) {
  .features-grid {
    gap: 20rpx;
  }

  .feature-item {
    padding: 25rpx 20rpx;
  }
}

/* 动画效果 */
.container {
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.action-btn, .action-card {
  animation: slideUp 0.6s ease-out;
  animation-fill-mode: both;
}

.primary-action .action-btn {
  animation-delay: 0.1s;
}

.secondary-actions .action-card:nth-child(1) { animation-delay: 0.2s; }
.secondary-actions .action-card:nth-child(2) { animation-delay: 0.3s; }
.secondary-actions .action-card:nth-child(3) { animation-delay: 0.4s; }

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-item {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.feature-item:nth-child(1) { animation-delay: 0.5s; }
.feature-item:nth-child(2) { animation-delay: 0.6s; }
.feature-item:nth-child(3) { animation-delay: 0.7s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 签到模块样式 */
.checkin-section {
  padding: 0 30rpx;
  margin-bottom: 20rpx;
  animation: slideInUp 0.6s ease-out 0.4s both;
}

.checkin-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 25rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.checkin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18rpx;
}

.checkin-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.checkin-icon {
  font-size: 32rpx;
}

.checkin-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.checkin-streak {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.checkin-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.checkin-reward {
  flex: 1;
}

.reward-text {
  font-size: 26rpx;
  color: #666;
}

.checkin-action {
  margin-left: 20rpx;
}

.checkin-btn {
  padding: 16rpx 32rpx;
  border-radius: 25rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  min-width: 120rpx;
  transition: all 0.3s ease;
}

.checkin-btn.available {
  background: linear-gradient(135deg, #4caf50, #45a049);
  color: white;
  box-shadow: 0 4rpx 15rpx rgba(76, 175, 80, 0.3);
}

.checkin-btn.available:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
}

.checkin-btn.checked {
  background: #e0e0e0;
  color: #999;
}

.checkin-progress {
  text-align: center;
}

.progress-dots {
  display: flex;
  justify-content: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.progress-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #e0e0e0;
  transition: all 0.3s ease;
}

.progress-dot.active {
  background: linear-gradient(135deg, #4caf50, #45a049);
  transform: scale(1.2);
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
}

.progress-text {
  font-size: 24rpx;
  color: #666;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .header {
    padding: 10rpx 20rpx 20rpx;
  }

  .main-section {
    margin: 20rpx 20rpx 20rpx 20rpx;
    flex-direction: column;
    gap: 15rpx;
  }

  .user-status-compact {

  }

  .user-status-compact .user-info {
    margin-bottom: 8rpx;
  }

  .primary-action-compact .action-btn {
    min-height: 120rpx;
  }

  .checkin-section {
    padding: 0 20rpx;
    margin-bottom: 15rpx;
  }

  .main-actions {
    padding: 0 20rpx;
  }

  .secondary-actions {
    margin: 0 20rpx;
    width: calc(100% - 40rpx);
    gap: 15rpx;
  }

  .new-features-actions {
    padding: 0 20rpx;
    margin: 15rpx 0;
    gap: 15rpx;
  }



  .features {
    padding: 0 20rpx 20rpx;
    margin-top: 15rpx;
  }

  .features-grid {
    gap: 15rpx;
  }

  .checkin-card {
    padding: 20rpx;
  }

  .checkin-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 15rpx;
  }

  .checkin-action {
    margin-left: 0;
    align-self: stretch;
  }

  .checkin-btn {
    width: 100%;
  }
}

